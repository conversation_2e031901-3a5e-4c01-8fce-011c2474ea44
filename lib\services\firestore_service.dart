import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_model.dart';
import '../models/session_model.dart';
import '../models/finding_model.dart';
import '../models/area_model.dart';
import '../models/site_model.dart';
import 'firebase_service.dart';

/// Service class for Firestore operations
class FirestoreService {
  static FirebaseFirestore get _firestore => FirebaseService.firestore;

  // Collection references
  static CollectionReference get _usersCollection => _firestore.collection('users');
  static CollectionReference get _sitesCollection => _firestore.collection('sites');
  static CollectionReference get _areasCollection => _firestore.collection('areas');
  static CollectionReference get _sessionsCollection => _firestore.collection('sessions');

  /// Initialize Firestore collections and indexes
  static Future<void> initializeCollections() async {
    try {
      // Create initial indexes and collection structure
      await _createInitialData();
    } catch (e) {
      throw Exception('Failed to initialize Firestore collections: $e');
    }
  }

  /// Create initial data structure
  static Future<void> _createInitialData() async {
    // This would typically be done through Firebase Console
    // but we can create the structure programmatically for development
    
    // Ensure collections exist by creating a temporary document
    // and then deleting it (Firestore creates collections on first write)
    final batch = _firestore.batch();
    
    // Create temporary documents to initialize collections
    final tempUserRef = _usersCollection.doc('temp');
    final tempSiteRef = _sitesCollection.doc('temp');
    final tempAreaRef = _areasCollection.doc('temp');
    final tempSessionRef = _sessionsCollection.doc('temp');
    
    batch.set(tempUserRef, {'temp': true});
    batch.set(tempSiteRef, {'temp': true});
    batch.set(tempAreaRef, {'temp': true});
    batch.set(tempSessionRef, {'temp': true});
    
    await batch.commit();
    
    // Delete temporary documents
    final deleteBatch = _firestore.batch();
    deleteBatch.delete(tempUserRef);
    deleteBatch.delete(tempSiteRef);
    deleteBatch.delete(tempAreaRef);
    deleteBatch.delete(tempSessionRef);
    
    await deleteBatch.commit();
  }

  /// User operations
  static Future<void> createUser(UserModel user) async {
    await _usersCollection.doc(user.id).set(user.toMap());
  }

  static Future<UserModel?> getUser(String userId) async {
    final doc = await _usersCollection.doc(userId).get();
    if (doc.exists) {
      return UserModel.fromMap(doc.data() as Map<String, dynamic>);
    }
    return null;
  }

  static Future<void> updateUser(UserModel user) async {
    await _usersCollection.doc(user.id).update(user.toMap());
  }

  /// Site operations
  static Future<void> createSite(SiteModel site) async {
    await _sitesCollection.doc(site.id).set(site.toMap());
  }

  static Future<List<SiteModel>> getSites() async {
    final snapshot = await _sitesCollection.get();
    return snapshot.docs
        .map((doc) => SiteModel.fromMap(doc.data() as Map<String, dynamic>))
        .toList();
  }

  /// Area operations
  static Future<void> createArea(AreaModel area) async {
    await _areasCollection.doc(area.id).set(area.toMap());
  }

  static Future<List<AreaModel>> getAreasBySite(String siteId) async {
    final snapshot = await _areasCollection
        .where('siteId', isEqualTo: siteId)
        .get();
    return snapshot.docs
        .map((doc) => AreaModel.fromMap(doc.data() as Map<String, dynamic>))
        .toList();
  }

  /// Session operations
  static Future<void> createSession(SessionModel session) async {
    await _sessionsCollection.doc(session.id).set(session.toMap());
  }

  static Future<SessionModel?> getSession(String sessionId) async {
    final doc = await _sessionsCollection.doc(sessionId).get();
    if (doc.exists) {
      return SessionModel.fromMap(doc.data() as Map<String, dynamic>);
    }
    return null;
  }

  static Future<List<SessionModel>> getUserSessions(String userId) async {
    final snapshot = await _sessionsCollection
        .where('leaderId', isEqualTo: userId)
        .get();
    return snapshot.docs
        .map((doc) => SessionModel.fromMap(doc.data() as Map<String, dynamic>))
        .toList();
  }

  /// Finding operations
  static Future<void> createFinding(String sessionId, FindingModel finding) async {
    await _sessionsCollection
        .doc(sessionId)
        .collection('findings')
        .doc(finding.id)
        .set(finding.toMap());
  }

  static Future<List<FindingModel>> getSessionFindings(String sessionId) async {
    final snapshot = await _sessionsCollection
        .doc(sessionId)
        .collection('findings')
        .get();
    return snapshot.docs
        .map((doc) => FindingModel.fromMap(doc.data() as Map<String, dynamic>))
        .toList();
  }

  static Future<void> updateFinding(String sessionId, FindingModel finding) async {
    await _sessionsCollection
        .doc(sessionId)
        .collection('findings')
        .doc(finding.id)
        .update(finding.toMap());
  }

  /// Batch operations for offline sync
  static Future<void> batchWrite(List<Map<String, dynamic>> operations) async {
    final batch = _firestore.batch();
    
    for (final operation in operations) {
      final type = operation['type'] as String;
      final collection = operation['collection'] as String;
      final docId = operation['docId'] as String;
      final data = operation['data'] as Map<String, dynamic>;
      
      final docRef = _firestore.collection(collection).doc(docId);
      
      switch (type) {
        case 'create':
        case 'update':
          batch.set(docRef, data, SetOptions(merge: true));
          break;
        case 'delete':
          batch.delete(docRef);
          break;
      }
    }
    
    await batch.commit();
  }
}
