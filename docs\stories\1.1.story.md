# Story 1.1: Project Setup & Firebase Integration

## Status

Ready for Review

## Story

**As a** developer,
**I want** to establish the foundational app infrastructure,
**so that** users can securely access the app and their data is reliably stored both online and offline.

## Acceptance Criteria

1. 1.1.1: Flutter project created with iOS 12.0+ and Android 8.0+ support.
2. 1.1.2: Firebase Authentication (email/password, Google/Azure AD SSO) integrated with offline credential caching in SQLite.
3. 1.1.3: Firestore configured for storing users, sessions, findings, and areas.
4. 1.1.4: SQLite (`sqflite`) implemented for offline caching of login credentials and basic data models.
5. 1.1.5: Basic health-check route displays a "Welcome to SafeStride" screen.
6. 1.1.6: Unit tests for Firebase and SQLite integration.

## Tasks / Subtasks

- [ ] Task 1: Initialize Flutter Project (AC: 1.1.1)
  - [ ] Create new Flutter project with minimum iOS 12.0+ and Android 8.0+ support
  - [ ] Configure pubspec.yaml with required dependencies
  - [ ] Set up project structure following coding standards
  - [ ] Configure build settings for target platforms

- [ ] Task 2: Firebase Integration Setup (AC: 1.1.2, 1.1.3)
  - [ ] Create Firebase project and configure for iOS/Android
  - [ ] Add Firebase configuration files (google-services.json, GoogleService-Info.plist)
  - [ ] Initialize Firebase Auth with email/password and SSO providers
  - [ ] Configure Firestore database with security rules
  - [ ] Set up Firebase project structure for users, sessions, findings, areas collections

- [ ] Task 3: SQLite Local Storage Implementation (AC: 1.1.2, 1.1.4)
  - [ ] Integrate sqflite package for local database
  - [ ] Create SQLite schema for users, sessions, findings tables
  - [ ] Implement offline credential caching functionality
  - [ ] Create database helper classes following repository pattern

- [ ] Task 4: Data Models and Repository Pattern (AC: 1.1.3, 1.1.4)
  - [ ] Create User, Session, Finding, Site, Area data models
  - [ ] Implement repository interfaces for data access abstraction
  - [ ] Create Firebase and SQLite repository implementations
  - [ ] Implement sync mechanism between local and cloud storage

- [ ] Task 5: Basic UI and Health Check (AC: 1.1.5)
  - [ ] Create welcome screen with "Welcome to SafeStride" message
  - [ ] Set up basic routing and navigation structure
  - [ ] Implement health-check route for app verification
  - [ ] Apply basic styling following design guidelines

- [ ] Task 6: Unit Testing Implementation (AC: 1.1.6)
  - [ ] Set up testing framework and test structure
  - [ ] Write unit tests for Firebase authentication integration
  - [ ] Write unit tests for SQLite database operations
  - [ ] Write unit tests for data models and repository implementations
  - [ ] Achieve minimum 80% code coverage for business logic

## Dev Notes

### Previous Story Insights

No previous stories exist - this is the foundational story.

### Tech Stack Requirements

[Source: architecture/tech-stack.md]

- **Flutter Version:** 3.16.0 with Dart 3.2.0
- **Firebase Services:** Latest versions of Auth, Firestore, Cloud Functions, Cloud Storage
- **Local Storage:** SQLite via sqflite 2.3.0
- **State Management:** Provider 6.1.1 pattern
- **Image Processing:** image_picker 1.0.4 for future photo features
- **Deployment:** Multi-region via Firebase (automatic)

### Data Models and Schema

[Source: architecture/data-models.md, architecture/database-schema.md]

**User Model:**

- id: String (Firebase Auth UID)
- email: String
- name: String
- role: Enum (Leader, Observer)
- subscription: Enum (Free, Premium)
- createdAt: DateTime

**SQLite Schema for Local Caching:**

```sql
CREATE TABLE users (
  id TEXT PRIMARY KEY,
  email TEXT NOT NULL,
  name TEXT,
  role TEXT,
  subscription TEXT,
  synced INTEGER DEFAULT 0
);

CREATE TABLE sessions (
  id TEXT PRIMARY KEY,
  type TEXT NOT NULL,
  leader_id TEXT,
  area_id TEXT,
  status TEXT,
  invite_code TEXT,
  created_at INTEGER,
  synced INTEGER DEFAULT 0
);

CREATE TABLE findings (
  id TEXT PRIMARY KEY,
  session_id TEXT,
  description TEXT,
  severity TEXT,
  category TEXT,
  photo_path TEXT,
  author_id TEXT,
  status TEXT,
  created_at INTEGER,
  synced INTEGER DEFAULT 0,
  FOREIGN KEY (session_id) REFERENCES sessions(id)
);
```

**Firestore Collections Structure:**

```
/users/{userId}
/sites/{siteId}
/areas/{areaId}
/sessions/{sessionId}
/sessions/{sessionId}/findings/{findingId}
```

### Architectural Patterns

[Source: architecture/high-level-architecture.md]

- **Repository Pattern:** Abstract data access layer for offline/online switching
- **Provider Pattern:** Flutter state management for reactive UI
- **Offline-First Pattern:** SQLite + Firestore sync for industrial environments
- **Serverless Architecture:** Firebase backend eliminates infrastructure management

### Security Requirements

[Source: architecture/security.md]

- **Authentication:** Multi-factor auth available, role-based access control
- **Data Protection:** Encryption at rest (Firestore) and in transit (TLS 1.3)
- **Secrets Management:** Environment variables and secure device storage
- **Input Validation:** Client-side feedback + server-side Firestore rules

### File Structure and Naming

[Source: architecture/coding-standards.md]

- **Files:** snake_case (user_repository.dart, session_service.dart)
- **Classes:** PascalCase (UserRepository, SessionService)
- **Variables/Functions:** camelCase (getCurrentUser, sessionId)
- **Constants:** SCREAMING_SNAKE_CASE (MAX_PHOTO_SIZE)

### Critical Implementation Rules

[Source: architecture/coding-standards.md]

1. No direct Firebase calls in UI - use repository pattern
2. Always handle offline state - check connectivity before network calls
3. Validate all user inputs - client and server-side validation
4. Encrypt sensitive data - use secure storage for credentials
5. Log errors appropriately - include context but protect privacy

### Testing

[Source: architecture/test-strategy.md]

- **Test Pyramid:** Unit tests (70%) > Integration tests (20%) > E2E tests (10%)
- **Coverage Requirement:** Minimum 80% code coverage for business logic
- **Offline-First Testing:** All core features must work without network
- **Test Types:**
  - Unit Tests: Business logic, data models, repository implementations
  - Integration Tests: Firebase service integration, SQLite operations
  - Mocking: Mock external dependencies for unit tests
- **Test Data:** Isolated test Firebase project for testing
- **Device Testing:** Test on minimum supported devices (iPhone 7, Android 8.0)

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2024-12-19 | 1.0 | Initial story creation | Scrum Master |

## Dev Agent Record

*This section will be populated by the development agent during implementation*

### Agent Model Used

*To be filled by dev agent*

### Debug Log References

*To be filled by dev agent*

### Completion Notes List

*To be filled by dev agent*

### File List

*To be filled by dev agent*

## QA Results

*Results from QA Agent review will be populated here after implementation*
